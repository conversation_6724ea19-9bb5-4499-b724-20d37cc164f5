import pandas as pd
import os

# Create data directory if it doesn't exist
if not os.path.exists('data'):
    os.makedirs('data')

# Load the original dataset
print("Loading original dataset...")
df = pd.read_csv('PS_20174392719_1491204439457_log.csv')

# Basic data cleaning and processing
print("Cleaning and processing data...")

# Check for missing values
print("\nMissing values:")
print(df.isnull().sum())

# Remove duplicates if any
df = df.drop_duplicates()
print(f"\nShape after removing duplicates: {df.shape}")

# Convert step to int and amount to float if needed
df['step'] = df['step'].astype(int)
df['amount'] = df['amount'].astype(float)

# Save the cleaned dataset
output_path = 'data/cleaned_transactions.csv'
df.to_csv(output_path, index=False)
print(f"\nCleaned dataset saved to: {output_path}")
print("Data preprocessing complete!")
