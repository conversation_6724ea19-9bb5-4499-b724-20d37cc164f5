import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Set page config
st.set_page_config(
    page_title="Mobile Money Transaction Dashboard",
    page_icon="💰",
    layout="wide"
)

# Load data
@st.cache_data
def load_data():
    try:
        df = pd.read_csv('data/cleaned_transactions.csv')
    except FileNotFoundError:
        st.error("Cleaned data file not found. Please run Week 1 notebook first to generate the cleaned dataset!")
        st.stop()
    return df

# Load the data
df = load_data()

# Sidebar filters
st.sidebar.title('Filters')

# Transaction type filter
transaction_types = ['All'] + list(df['type'].unique())
selected_type = st.sidebar.selectbox('Transaction Type', transaction_types)

# Fraud filter
fraud_filter = st.sidebar.selectbox('Fraud Status', ['All', 'Fraudulent', 'Non-Fraudulent'])

# Amount range filter
amount_range = st.sidebar.slider(
    'Amount Range',
    min_value=float(df['amount'].min()),
    max_value=float(df['amount'].max()),
    value=(float(df['amount'].min()), float(df['amount'].max()))
)

# Filter data based on selections
filtered_df = df.copy()

if selected_type != 'All':
    filtered_df = filtered_df[filtered_df['type'] == selected_type]

if fraud_filter != 'All':
    filtered_df = filtered_df[filtered_df['isFraud'] == (fraud_filter == 'Fraudulent')]

filtered_df = filtered_df[
    (filtered_df['amount'] >= amount_range[0]) &
    (filtered_df['amount'] <= amount_range[1])
]

# Main dashboard
st.title('Mobile Money Transaction Dashboard')

# Key metrics
col1, col2, col3, col4 = st.columns(4)

with col1:
    st.metric(
        "Total Transactions",
        f"{len(filtered_df):,}"
    )

with col2:
    st.metric(
        "Total Volume",
        f"${filtered_df['amount'].sum():,.2f}"
    )

with col3:
    fraud_rate = (filtered_df['isFraud'].mean() * 100)
    st.metric(
        "Fraud Rate",
        f"{fraud_rate:.2f}%"
    )

with col4:
    avg_amount = filtered_df['amount'].mean()
    st.metric(
        "Average Amount",
        f"${avg_amount:,.2f}"
    )

# Transaction patterns
st.subheader('Transaction Patterns')
col1, col2 = st.columns(2)

with col1:
    # Transaction type distribution
    type_counts = filtered_df['type'].value_counts()
    fig = px.pie(
        values=type_counts.values,
        names=type_counts.index,
        title='Transaction Type Distribution'
    )
    st.plotly_chart(fig, use_container_width=True)

with col2:
    # Amount distribution
    fig = px.histogram(
        filtered_df,
        x='amount',
        title='Transaction Amount Distribution',
        nbins=50
    )
    st.plotly_chart(fig, use_container_width=True)

# Time series analysis
st.subheader('Time Series Analysis')
time_data = filtered_df.groupby('step').agg({
    'amount': 'sum',
    'isFraud': 'mean'
}).reset_index()

fig = make_subplots(specs=[[{"secondary_y": True}]])

fig.add_trace(
    go.Scatter(
        x=time_data['step'],
        y=time_data['amount'],
        name="Transaction Volume"
    ),
    secondary_y=False
)

fig.add_trace(
    go.Scatter(
        x=time_data['step'],
        y=time_data['isFraud'] * 100,
        name="Fraud Rate (%)"
    ),
    secondary_y=True
)

fig.update_layout(
    title='Transaction Volume and Fraud Rate Over Time',
    xaxis_title='Time Step',
    yaxis_title="Transaction Volume ($)",
    yaxis2_title="Fraud Rate (%)"
)

st.plotly_chart(fig, use_container_width=True)

# Detailed Transaction Table
st.subheader('Transaction Details')

# Add pagination controls
rows_per_page = st.selectbox('Rows per page', [10, 20, 50, 100], index=1)
total_pages = len(filtered_df) // rows_per_page + (1 if len(filtered_df) % rows_per_page > 0 else 0)
current_page = st.number_input('Page', min_value=1, max_value=total_pages, value=1)

# Calculate start and end indices for the current page
start_idx = (current_page - 1) * rows_per_page
end_idx = min(start_idx + rows_per_page, len(filtered_df))

# Display page information
st.write(f'Showing {start_idx + 1} to {end_idx} of {len(filtered_df)} entries')

# Display paginated dataframe
st.dataframe(
    filtered_df[['step', 'type', 'amount', 'isFraud', 'oldbalanceOrg', 'newbalanceOrig', 'oldbalanceDest', 'newbalanceDest']]
    .iloc[start_idx:end_idx]
)
