{"cells": [{"cell_type": "markdown", "id": "9c54d8d5", "metadata": {}, "source": ["# Week 1: Mobile Money Transaction Analysis - Data Cleaning and Exploration\n", "\n", "This notebook covers the first week of our mobile money transaction analysis project. We'll focus on:\n", "\n", "1. Loading and examining the dataset\n", "2. Understanding the data structure and creating a data dictionary\n", "3. Cleaning the data and handling any issues\n", "4. Saving the cleaned dataset for future analysis\n", "\n", "Let's begin by importing the necessary libraries and loading our data."]}, {"cell_type": "code", "execution_count": null, "id": "2a602ba0", "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd  # For data manipulation and analysis\n", "import numpy as np   # For numerical operations\n", "import matplotlib.pyplot as plt  # For creating static visualizations\n", "import seaborn as sns  # For statistical visualizations\n", "import plotly.express as px  # For interactive visualizations\n", "\n", "# Set display options for better visibility\n", "pd.set_option('display.max_columns', None)  # Show all columns\n", "pd.set_option('display.max_rows', 100)      # Show up to 100 rows\n", "pd.set_option('display.width', 1000)        # Set display width\n", "\n", "# Set plotting style\n", "plt.style.use('seaborn')  # Use seaborn style for better-looking plots\n", "sns.set_palette(\"husl\")   # Set a color palette for visualizations"]}, {"cell_type": "markdown", "id": "21eefd74", "metadata": {}, "source": ["## 1. Loading the Dataset\n", "\n", "Now we'll load our mobile money transaction dataset. The dataset contains information about various transactions including:\n", "- Transaction steps (time)\n", "- Transaction types\n", "- Amount transferred\n", "- Origin and destination account information\n", "- Balance information\n", "- Fraud labels\n", "\n", "Let's load the data and take a first look at its structure."]}, {"cell_type": "code", "execution_count": null, "id": "03a1c877", "metadata": {}, "outputs": [], "source": ["# Load the dataset\n", "df = pd.read_csv('../DataTransactions.csv')\n", "\n", "# Display basic information about the dataset\n", "print(\"Dataset Shape:\", df.shape)\n", "print(\"\\nFirst few rows of the dataset:\")\n", "display(df.head())\n", "\n", "# Display basic information about the columns\n", "print(\"\\nDataset Information:\")\n", "display(df.info())"]}, {"cell_type": "markdown", "id": "0fe3e5ed", "metadata": {}, "source": ["## 2. Data Dictionary\n", "\n", "Let's create a comprehensive data dictionary to understand each column in our dataset:\n", "\n", "| Column Name | Description | Data Type | Example Values |\n", "|-------------|-------------|------------|----------------|\n", "| step | Represents the time step in the simulation (1 step = 1 hour) | Integer | 1, 2, 3, ... |\n", "| type | Type of mobile money transaction | String | CASH_IN, CASH_OUT, DEBIT, PAYMENT, TRANSFER |\n", "| amount | Amount of money transferred | Float | 181.00, 229.04, ... |\n", "| nameOrig | Customer who initiated the transaction | String | C1231006815, ... |\n", "| oldbalanceOrg | Initial balance of originator before transaction | Float | 181.00, 0.00, ... |\n", "| newbalanceOrig | New balance of originator after transaction | Float | 0.00, 181.00, ... |\n", "| nameDest | Customer receiving the transaction | String | M1979787155, ... |\n", "| oldbalanceDest | Initial balance of recipient before transaction | Float | 0.00, 21182.00, ... |\n", "| newbalanceDest | New balance of recipient after transaction | Float | 0.00, 21363.00, ... |\n", "| isFraud | Binary flag indicating if the transaction is fraudulent | Integer | 0 (No), 1 (Yes) |\n", "\n", "Now let's analyze the data types and check for any inconsistencies or missing values."]}, {"cell_type": "code", "execution_count": null, "id": "6207aa8f", "metadata": {}, "outputs": [], "source": ["# Check for missing values\n", "print(\"Missing values in each column:\")\n", "display(df.isnull().sum())\n", "\n", "# Check unique values in categorical columns\n", "print(\"\\nUnique transaction types:\")\n", "display(df['type'].value_counts())\n", "\n", "# Check for duplicates\n", "duplicate_count = df.duplicated().sum()\n", "print(f\"\\nNumber of duplicate rows: {duplicate_count}\")\n", "\n", "# Basic statistics of numerical columns\n", "print(\"\\nBasic statistics of numerical columns:\")\n", "display(df.describe())"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}