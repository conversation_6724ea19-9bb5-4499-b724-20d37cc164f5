# Import required libraries
import pandas as pd  # For data manipulation and analysis
import numpy as np   # For numerical operations
import matplotlib.pyplot as plt  # For basic plotting
import seaborn as sns  # For statistical visualizations
import plotly.express as px  # For interactive visualizations
import warnings

# Set display options for better readability
pd.set_option('display.max_columns', None)
warnings.filterwarnings('ignore')  # Suppress warnings for cleaner output

# Set visual styles
plt.style.use('seaborn')
sns.set_palette("husl")

# Load the dataset
# Note: Make sure the CSV file is in the correct location
df = pd.read_csv('../PS_20174392719_1491204439457_log.csv')

# Display first few rows and basic information about the dataset
print("First few rows of the dataset:")
display(df.head())

print("\nDataset Information:")
display(df.info())

# Check for missing values
print("Missing values in each column:")
display(df.isnull().sum())

# Check for duplicate transactions
print("\nNumber of duplicate rows:", df.duplicated().sum())

# Check data types and basic statistics
print("\nData types of each column:")
display(df.dtypes)

print("\nBasic statistics of numerical columns:")
display(df.describe())

# Analyze transaction types
transaction_types = df['type'].value_counts()
print("Transaction type distribution:")
display(transaction_types)

# Create a pie chart of transaction types
plt.figure(figsize=(10, 6))
plt.pie(transaction_types.values, labels=transaction_types.index, autopct='%1.1f%%')
plt.title('Distribution of Transaction Types')
plt.show()

# Calculate fraud rate by transaction type
fraud_by_type = df.groupby('type')['isFraud'].mean() * 100
print("\nFraud rate by transaction type (%):")
display(fraud_by_type)

# Create a data dictionary
data_dict = {
    'Column Name': df.columns,
    'Data Type': df.dtypes.values,
    'Non-Null Count': df.count().values,
    'Unique Values': [df[col].nunique() for col in df.columns]
}

data_dictionary = pd.DataFrame(data_dict)
print("Data Dictionary:")
display(data_dictionary)

# Save the clean dataset
df.to_csv('../data/cleaned_transactions.csv', index=False)
print("\nCleaned dataset saved to 'data/cleaned_transactions.csv'")