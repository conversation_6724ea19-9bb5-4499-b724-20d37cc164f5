# Import required libraries
import pandas as pd  # For data manipulation and analysis
import numpy as np   # For numerical operations
import matplotlib.pyplot as plt  # For basic plotting
import seaborn as sns  # For statistical visualizations
import plotly.express as px  # For interactive visualizations
import warnings

# Set display options for better readability
pd.set_option('display.max_columns', None)
warnings.filterwarnings('ignore')  # Suppress warnings for cleaner output

# Set visual styles
plt.style.use('default')  # Use default matplotlib style
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (10, 6)  # Set default figure size

# Check package versions
import sys
print(f"Python version: {sys.version}")
print(f"Pandas version: {pd.__version__}")
print(f"NumPy version: {np.__version__}")
print(f"Matplotlib version: {plt.matplotlib.__version__}")
print(f"Seaborn version: {sns.__version__}")
print(f"Plotly version: {px.__version__}")
print("\nAll packages loaded successfully!")

# Load the dataset
import os

# Try different possible locations for the dataset
possible_paths = [
    '../data/PS_20174392719_1491204439457_log.csv',  # Data directory (primary)
    'data/PS_20174392719_1491204439457_log.csv',     # Data directory (from root)
    '../PS_20174392719_1491204439457_log.csv'        # Parent directory (fallback)
]

df = None
for path in possible_paths:
    if os.path.exists(path):
        print(f"Loading dataset from: {path}")
        df = pd.read_csv(path)
        break

if df is None:
    print("Dataset not found! Please ensure the PaySim dataset CSV file is in the project directory.")
    print("Expected filename: PS_20174392719_1491204439457_log.csv")
    print("You can download it from: https://www.kaggle.com/datasets/ealaxi/paysim1")
else:
    print(f"Dataset loaded successfully! Shape: {df.shape}")
    
    # Display first few rows and basic information about the dataset
    print("\nFirst few rows of the dataset:")
    display(df.head())
    
    print("\nDataset Information:")
    display(df.info())
    
    print(f"\nDataset contains {len(df):,} transactions")
    print(f"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")

# Only proceed if dataset was loaded successfully
if df is not None:
    # Check for missing values
    print("Missing values in each column:")
    missing_values = df.isnull().sum()
    display(missing_values)
    
    if missing_values.sum() == 0:
        print("✓ No missing values found!")
    else:
        print("⚠ Missing values detected - will need to handle these")
    
    # Check for duplicate transactions
    duplicates = df.duplicated().sum()
    print(f"\nNumber of duplicate rows: {duplicates}")
    if duplicates == 0:
        print("✓ No duplicate transactions found!")
    else:
        print("⚠ Duplicate transactions detected - will need to handle these")
    
    # Check data types and basic statistics
    print("\nData types of each column:")
    display(df.dtypes)
    
    print("\nBasic statistics of numerical columns:")
    display(df.describe())
    
    # Check for any obvious data quality issues
    print("\nData Quality Checks:")
    print(f"- Negative amounts: {(df['amount'] < 0).sum()}")
    print(f"- Zero amounts: {(df['amount'] == 0).sum()}")
    print(f"- Fraud transactions: {df['isFraud'].sum():,} ({df['isFraud'].mean()*100:.3f}%)")
else:
    print("Cannot proceed with data cleaning - dataset not loaded.")

# Only proceed if dataset was loaded successfully
if df is not None:
    # Analyze transaction types
    transaction_types = df['type'].value_counts()
    print("Transaction type distribution:")
    display(transaction_types)
    
    # Create a pie chart of transaction types
    plt.figure(figsize=(12, 8))
    
    # Create subplot for pie chart and bar chart
    plt.subplot(1, 2, 1)
    plt.pie(transaction_types.values, labels=transaction_types.index, autopct='%1.1f%%', startangle=90)
    plt.title('Distribution of Transaction Types')
    
    # Create bar chart for better readability
    plt.subplot(1, 2, 2)
    transaction_types.plot(kind='bar', color='skyblue')
    plt.title('Transaction Count by Type')
    plt.xlabel('Transaction Type')
    plt.ylabel('Count')
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    plt.show()
    
    # Calculate fraud rate by transaction type
    fraud_by_type = df.groupby('type')['isFraud'].agg(['sum', 'count', 'mean']).round(4)
    fraud_by_type.columns = ['Fraud_Count', 'Total_Count', 'Fraud_Rate']
    fraud_by_type['Fraud_Rate_Percent'] = fraud_by_type['Fraud_Rate'] * 100
    
    print("\nFraud analysis by transaction type:")
    display(fraud_by_type)
    
    # Visualize fraud rates
    plt.figure(figsize=(10, 6))
    fraud_by_type['Fraud_Rate_Percent'].plot(kind='bar', color='red', alpha=0.7)
    plt.title('Fraud Rate by Transaction Type')
    plt.xlabel('Transaction Type')
    plt.ylabel('Fraud Rate (%)')
    plt.xticks(rotation=45)
    plt.grid(axis='y', alpha=0.3)
    plt.tight_layout()
    plt.show()
else:
    print("Cannot proceed with transaction analysis - dataset not loaded.")

# Only proceed if dataset was loaded successfully
if df is not None:
    # Create a comprehensive data dictionary
    data_dict = {
        'Column Name': df.columns,
        'Data Type': df.dtypes.values,
        'Non-Null Count': df.count().values,
        'Unique Values': [df[col].nunique() for col in df.columns],
        'Sample Values': [str(df[col].unique()[:3]) if df[col].nunique() <= 10 else str(df[col].unique()[:3]) + '...' for col in df.columns]
    }
    
    data_dictionary = pd.DataFrame(data_dict)
    print("Data Dictionary:")
    display(data_dictionary)
    
    # Create detailed column descriptions
    column_descriptions = {
        'step': 'Time step in simulation (1 step = 1 hour)',
        'type': 'Transaction type (CASH_IN, CASH_OUT, DEBIT, PAYMENT, TRANSFER)',
        'amount': 'Transaction amount in local currency units',
        'nameOrig': 'Customer ID who initiated the transaction',
        'nameDest': 'Customer ID who received the transaction',
        'oldbalanceOrg': 'Initial balance of sender before transaction',
        'newbalanceOrig': 'New balance of sender after transaction',
        'oldbalanceDest': 'Initial balance of receiver before transaction',
        'newbalanceDest': 'New balance of receiver after transaction',
        'isFraud': 'Binary fraud indicator (1=fraud, 0=legitimate)'
    }
    
    detailed_dict = pd.DataFrame([
        {'Column': col, 'Description': column_descriptions.get(col, 'No description available')}
        for col in df.columns
    ])
    
    print("\nDetailed Column Descriptions:")
    display(detailed_dict)
    
    # Ensure data directory exists
    os.makedirs('../data', exist_ok=True)
    
    # Save the clean dataset
    df.to_csv('../data/cleaned_transactions.csv', index=False)
    print("\n✓ Cleaned dataset saved to 'data/cleaned_transactions.csv'")
    
    # Save data dictionary as well
    data_dictionary.to_csv('../data/data_dictionary.csv', index=False)
    detailed_dict.to_csv('../data/column_descriptions.csv', index=False)
    print("✓ Data dictionary saved to 'data/data_dictionary.csv'")
    print("✓ Column descriptions saved to 'data/column_descriptions.csv'")
    
    # Print summary statistics
    print(f"\n=== WEEK 1 SUMMARY ===")
    print(f"Dataset shape: {df.shape}")
    print(f"Total transactions: {len(df):,}")
    print(f"Fraud transactions: {df['isFraud'].sum():,} ({df['isFraud'].mean()*100:.3f}%)")
    print(f"Transaction types: {df['type'].nunique()}")
    print(f"Unique customers: {df['nameOrig'].nunique() + df['nameDest'].nunique()}")
    print(f"Time period: {df['step'].min()} to {df['step'].max()} hours")
else:
    print("Cannot create data dictionary - dataset not loaded.")