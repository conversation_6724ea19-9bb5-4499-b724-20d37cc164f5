# Mobile Money Transaction Analysis Dashboard

This project analyzes mobile money transaction data to create an interactive dashboard for visualizing trends, anomalies, and key metrics.

## Project Structure

```
mobile money dashboard/
├── data/               # Data files
├── notebooks/         # Jupyter notebooks for analysis
├── src/              # Source code for the Streamlit dashboard
├── docs/             # Documentation files
└── README.md         # This file
```

## Project Timeline

### Week 1: Data Loading, Cleaning, and Familiarization
- Load and understand the dataset
- Clean data and handle any issues
- Create data dictionary
- Initial observations documented

### Week 2: Exploratory Data Analysis (EDA)
- Transaction analysis
- Fraud analysis
- Time-based trends
- Distribution analysis

### Week 3: Dashboard Development
- Create Streamlit app
- Implement interactive filters
- Add visualizations
- Local testing

### Week 4: Polish & Presentation
- Finalize dashboard
- Add documentation
- Prepare presentation

## Setup Instructions

1. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. Install required packages:
```bash
pip install -r requirements.txt
```

3. Launch Jupyter Notebook:
```bash
jupyter notebook
```

4. Run Streamlit Dashboard:
```bash
streamlit run src/app.py
```

## Tools Used
- Python
- pandas
- matplotlib/seaborn/plotly
- Streamlit
- Jupyter Notebook
