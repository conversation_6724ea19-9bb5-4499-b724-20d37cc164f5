{"cells": [{"cell_type": "markdown", "id": "86484146", "metadata": {}, "source": ["# Week 2: Mobile Money Transaction Analysis - Exploratory Data Analysis\n", "\n", "This notebook focuses on the exploratory data analysis (EDA) phase of our mobile money transaction project. We'll:\n", "1. Load the cleaned dataset from Week 1\n", "2. Create detailed visualizations of transaction patterns\n", "3. Analyze fraud patterns and time-based trends\n", "4. Generate statistical insights for the dashboard\n", "\n", "## Setup and Data Loading"]}, {"cell_type": "code", "execution_count": null, "id": "b062f37d", "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import warnings\n", "\n", "# Set display options\n", "pd.set_option('display.max_columns', None)\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting styles\n", "plt.style.use('seaborn')\n", "sns.set_palette(\"husl\")\n", "\n", "# Load the cleaned dataset\n", "df = pd.read_csv('../data/cleaned_transactions.csv')\n", "print(\"Dataset loaded successfully. Shape:\", df.shape)"]}, {"cell_type": "markdown", "id": "c1e0efad", "metadata": {}, "source": ["## Transaction Patterns Analysis\n", "\n", "Let's analyze various patterns in the transactions:\n", "1. Transaction amounts distribution\n", "2. Time-based patterns\n", "3. Transaction types and their characteristics\n", "4. Balance patterns before and after transactions"]}, {"cell_type": "code", "execution_count": null, "id": "3c115947", "metadata": {}, "outputs": [], "source": ["# Analyze transaction amounts\n", "plt.figure(figsize=(12, 6))\n", "sns.histplot(data=df, x='amount', bins=50)\n", "plt.title('Distribution of Transaction Amounts')\n", "plt.xlabel('Amount')\n", "plt.ylabel('Count')\n", "plt.show()\n", "\n", "# Create box plots for amount by transaction type\n", "plt.figure(figsize=(12, 6))\n", "sns.boxplot(data=df, x='type', y='amount')\n", "plt.title('Transaction Amounts by Type')\n", "plt.xticks(rotation=45)\n", "plt.show()\n", "\n", "# Analyze transaction patterns over time\n", "transactions_over_time = df.groupby('step').size()\n", "plt.figure(figsize=(12, 6))\n", "plt.plot(transactions_over_time.index, transactions_over_time.values)\n", "plt.title('Transaction Volume Over Time')\n", "plt.xlabel('Time Step')\n", "plt.ylabel('Number of Transactions')\n", "plt.show()"]}, {"cell_type": "markdown", "id": "9f8d9e33", "metadata": {}, "source": ["## Fraud Analysis\n", "\n", "Let's analyze the fraud patterns in our dataset:\n", "1. Fraud rate by transaction type\n", "2. Amount patterns in fraudulent transactions\n", "3. Time patterns in fraud occurrences\n", "4. Balance patterns in fraudulent transactions"]}, {"cell_type": "code", "execution_count": null, "id": "e370829b", "metadata": {}, "outputs": [], "source": ["# Calculate fraud rate by transaction type\n", "fraud_by_type = df.groupby('type')['isFraud'].mean() * 100\n", "\n", "# Create bar plot of fraud rates\n", "plt.figure(figsize=(10, 6))\n", "fraud_by_type.plot(kind='bar')\n", "plt.title('Fraud Rate by Transaction Type')\n", "plt.xlabel('Transaction Type')\n", "plt.ylabel('Fraud Rate (%)')\n", "plt.xticks(rotation=45)\n", "plt.show()\n", "\n", "# Compare amount distributions for fraudulent vs non-fraudulent transactions\n", "plt.figure(figsize=(12, 6))\n", "plt.subplot(1, 2, 1)\n", "sns.boxplot(data=df, x='isFraud', y='amount')\n", "plt.title('Transaction Amounts by Fraud Status')\n", "\n", "plt.subplot(1, 2, 2)\n", "sns.histplot(data=df, x='amount', hue='isFraud', multiple=\"stack\")\n", "plt.title('Amount Distribution by Fraud Status')\n", "plt.show()\n", "\n", "# Analyze fraud patterns over time\n", "fraud_over_time = df.groupby('step')['isFraud'].mean() * 100\n", "plt.figure(figsize=(12, 6))\n", "plt.plot(fraud_over_time.index, fraud_over_time.values)\n", "plt.title('Fraud Rate Over Time')\n", "plt.xlabel('Time Step')\n", "plt.ylabel('Fraud Rate (%)')\n", "plt.show()"]}, {"cell_type": "markdown", "id": "ba32a752", "metadata": {}, "source": ["## Balance Analysis\n", "\n", "Let's analyze the balance patterns:\n", "1. Balance changes in transactions\n", "2. Relationship between old and new balances\n", "3. Balance patterns in fraudulent transactions\n", "4. Zero-balance transactions"]}, {"cell_type": "code", "execution_count": null, "id": "c0e81b13", "metadata": {}, "outputs": [], "source": ["# Calculate balance changes\n", "df['balance_change_orig'] = df['newbalanceOrig'] - df['oldbalanceOrg']\n", "df['balance_change_dest'] = df['newbalanceDest'] - df['oldbalanceDest']\n", "\n", "# Analyze balance changes\n", "plt.figure(figsize=(12, 6))\n", "plt.subplot(1, 2, 1)\n", "sns.boxplot(data=df, x='type', y='balance_change_orig')\n", "plt.title('Origin Balance Changes by Type')\n", "plt.xticks(rotation=45)\n", "\n", "plt.subplot(1, 2, 2)\n", "sns.boxplot(data=df, x='type', y='balance_change_dest')\n", "plt.title('Destination Balance Changes by Type')\n", "plt.xticks(rotation=45)\n", "plt.show()\n", "\n", "# Analyze zero-balance transactions\n", "zero_balance = {\n", "    'Origin Old Balance': (df['oldbalanceOrg'] == 0).mean() * 100,\n", "    'Origin New Balance': (df['newbalanceOrig'] == 0).mean() * 100,\n", "    'Destination Old Balance': (df['oldbalanceDest'] == 0).mean() * 100,\n", "    'Destination New Balance': (df['newbalanceDest'] == 0).mean() * 100\n", "}\n", "\n", "plt.figure(figsize=(10, 6))\n", "plt.bar(zero_balance.keys(), zero_balance.values())\n", "plt.title('Percentage of Zero-Balance Occurrences')\n", "plt.xticks(rotation=45)\n", "plt.ylabel('Percentage (%)')\n", "plt.show()"]}, {"cell_type": "markdown", "id": "f86e8095", "metadata": {}, "source": ["## Summary of Week 2 Findings\n", "\n", "In this notebook, we have:\n", "1. Analyzed transaction patterns and distributions\n", "2. Investigated fraud patterns and their characteristics\n", "3. Examined balance behaviors and zero-balance transactions\n", "4. Created visualizations for the key metrics\n", "\n", "Key Insights:\n", "- Transaction amount distributions vary significantly by type\n", "- Fraud patterns show specific characteristics in amounts and timing\n", "- Balance changes reveal interesting patterns in transaction behavior\n", "\n", "Next Steps (Week 3):\n", "- Develop Streamlit dashboard\n", "- Create interactive visualizations\n", "- Implement filtering capabilities\n", "- Add key metrics and insights"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}