{"cells": [{"cell_type": "markdown", "id": "86484146", "metadata": {}, "source": ["# Week 2: Mobile Money Transaction Analysis - Exploratory Data Analysis\n", "\n", "This notebook focuses on the exploratory data analysis (EDA) phase of our mobile money transaction project. We'll:\n", "1. Load the cleaned dataset from Week 1\n", "2. Create detailed visualizations of transaction patterns\n", "3. Analyze fraud patterns and time-based trends\n", "4. Generate statistical insights for the dashboard\n", "\n", "## Setup and Data Loading"]}, {"cell_type": "code", "execution_count": null, "id": "b062f37d", "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import warnings\n", "\n", "# Set display options\n", "pd.set_option('display.max_columns', None)\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting styles\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "plt.rcParams['figure.figsize'] = (12, 6)\n", "\n", "# Load the cleaned dataset with error handling\n", "import os\n", "\n", "data_path = '../data/cleaned_transactions.csv'\n", "if os.path.exists(data_path):\n", "    df = pd.read_csv(data_path)\n", "    print(f\"✓ Dataset loaded successfully. Shape: {df.shape}\")\n", "    print(f\"✓ Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "    print(f\"✓ Date range: Step {df['step'].min()} to {df['step'].max()}\")\n", "else:\n", "    print(\"❌ Cleaned dataset not found!\")\n", "    print(\"Please run Week 1 notebook first to generate the cleaned dataset.\")\n", "    df = None"]}, {"cell_type": "markdown", "id": "c1e0efad", "metadata": {}, "source": ["## Transaction Patterns Analysis\n", "\n", "Let's analyze various patterns in the transactions:\n", "1. Transaction amounts distribution\n", "2. Time-based patterns\n", "3. Transaction types and their characteristics\n", "4. Balance patterns before and after transactions"]}, {"cell_type": "code", "execution_count": null, "id": "3c115947", "metadata": {}, "outputs": [], "source": ["# Only proceed if dataset was loaded successfully\n", "if df is not None:\n", "    # 1. Transaction Amount Analysis\n", "    print(\"=== TRANSACTION AMOUNT ANALYSIS ===\")\n", "    print(f\"Amount statistics:\")\n", "    print(f\"- Mean: ${df['amount'].mean():,.2f}\")\n", "    print(f\"- Median: ${df['amount'].median():,.2f}\")\n", "    print(f\"- Max: ${df['amount'].max():,.2f}\")\n", "    print(f\"- Min: ${df['amount'].min():,.2f}\")\n", "    \n", "    # Create subplots for amount analysis\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    \n", "    # Histogram of amounts (log scale for better visualization)\n", "    axes[0,0].hist(df['amount'], bins=50, alpha=0.7, color='skyblue')\n", "    axes[0,0].set_xlabel('Amount')\n", "    axes[0,0].set_ylabel('Frequency')\n", "    axes[0,0].set_title('Distribution of Transaction Amounts')\n", "    axes[0,0].set_yscale('log')\n", "    \n", "    # Box plot by transaction type\n", "    df.boxplot(column='amount', by='type', ax=axes[0,1])\n", "    axes[0,1].set_title('Transaction Amounts by Type')\n", "    axes[0,1].set_xlabel('Transaction Type')\n", "    axes[0,1].set_ylabel('Amount')\n", "    axes[0,1].tick_params(axis='x', rotation=45)\n", "    \n", "    # Transaction volume over time\n", "    transactions_over_time = df.groupby('step').size()\n", "    axes[1,0].plot(transactions_over_time.index, transactions_over_time.values, color='green', alpha=0.7)\n", "    axes[1,0].set_xlabel('Time Step (Hours)')\n", "    axes[1,0].set_ylabel('Number of Transactions')\n", "    axes[1,0].set_title('Transaction Volume Over Time')\n", "    axes[1,0].grid(True, alpha=0.3)\n", "    \n", "    # Transaction type distribution\n", "    type_counts = df['type'].value_counts()\n", "    axes[1,1].pie(type_counts.values, labels=type_counts.index, autopct='%1.1f%%', startangle=90)\n", "    axes[1,1].set_title('Transaction Type Distribution')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Summary statistics by transaction type\n", "    print(\"\\n=== TRANSACTION STATISTICS BY TYPE ===\")\n", "    type_stats = df.groupby('type')['amount'].agg(['count', 'mean', 'median', 'std', 'min', 'max']).round(2)\n", "    display(type_stats)\n", "    \n", "else:\n", "    print(\"Cannot proceed with analysis - dataset not loaded.\")"]}, {"cell_type": "markdown", "id": "9f8d9e33", "metadata": {}, "source": ["## Fraud Analysis\n", "\n", "Let's analyze the fraud patterns in our dataset:\n", "1. Fraud rate by transaction type\n", "2. Amount patterns in fraudulent transactions\n", "3. Time patterns in fraud occurrences\n", "4. Balance patterns in fraudulent transactions"]}, {"cell_type": "code", "execution_count": null, "id": "e370829b", "metadata": {}, "outputs": [], "source": ["# Only proceed if dataset was loaded successfully\n", "if df is not None:\n", "    print(\"=== FRAUD ANALYSIS ===\")\n", "    \n", "    # Overall fraud statistics\n", "    total_fraud = df['isFraud'].sum()\n", "    fraud_rate = df['isFraud'].mean() * 100\n", "    print(f\"Total fraudulent transactions: {total_fraud:,}\")\n", "    print(f\"Overall fraud rate: {fraud_rate:.3f}%\")\n", "    \n", "    # Fraud analysis by transaction type\n", "    fraud_analysis = df.groupby('type').agg({\n", "        'isFraud': ['sum', 'count', 'mean']\n", "    }).round(4)\n", "    fraud_analysis.columns = ['Fraud_Count', 'Total_Count', 'Fraud_Rate']\n", "    fraud_analysis['Fraud_Rate_Percent'] = fraud_analysis['Fraud_Rate'] * 100\n", "    \n", "    print(\"\\nFraud statistics by transaction type:\")\n", "    display(fraud_analysis)\n", "    \n", "    # Create comprehensive fraud visualizations\n", "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "    \n", "    # 1. Fraud rate by transaction type\n", "    fraud_analysis['Fraud_Rate_Percent'].plot(kind='bar', ax=axes[0,0], color='red', alpha=0.7)\n", "    axes[0,0].set_title('Fraud Rate by Transaction Type')\n", "    axes[0,0].set_xlabel('Transaction Type')\n", "    axes[0,0].set_ylabel('Fraud Rate (%)')\n", "    axes[0,0].tick_params(axis='x', rotation=45)\n", "    axes[0,0].grid(axis='y', alpha=0.3)\n", "    \n", "    # 2. Amount comparison: <PERSON><PERSON> vs Non-fraud\n", "    fraud_amounts = df[df['isFraud'] == 1]['amount']\n", "    normal_amounts = df[df['isFraud'] == 0]['amount']\n", "    \n", "    axes[0,1].hist([normal_amounts, fraud_amounts], bins=50, alpha=0.7, \n", "                   label=['Normal', 'Fraud'], color=['blue', 'red'])\n", "    axes[0,1].set_xlabel('Transaction Amount')\n", "    axes[0,1].set_ylabel('Frequency')\n", "    axes[0,1].set_title('Amount Distribution: Normal vs Fraud')\n", "    axes[0,1].legend()\n", "    axes[0,1].set_yscale('log')\n", "    \n", "    # 3. <PERSON><PERSON> over time\n", "    fraud_over_time = df.groupby('step')['isFraud'].mean() * 100\n", "    axes[1,0].plot(fraud_over_time.index, fraud_over_time.values, color='red', alpha=0.7)\n", "    axes[1,0].set_xlabel('Time Step (Hours)')\n", "    axes[1,0].set_ylabel('Fraud Rate (%)')\n", "    axes[1,0].set_title('Fraud Rate Over Time')\n", "    axes[1,0].grid(True, alpha=0.3)\n", "    \n", "    # 4. Transaction volume: Normal vs Fraud\n", "    volume_comparison = df.groupby(['step', 'isFraud']).size().unstack(fill_value=0)\n", "    volume_comparison.plot(ax=axes[1,1], alpha=0.7)\n", "    axes[1,1].set_xlabel('Time Step (Hours)')\n", "    axes[1,1].set_ylabel('Number of Transactions')\n", "    axes[1,1].set_title('Transaction Volume: Normal vs Fraud')\n", "    axes[1,1].legend(['Normal', 'Fraud'])\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Statistical comparison of fraud vs normal transactions\n", "    print(\"\\n=== FRAUD vs NORMAL TRANSACTION COMPARISON ===\")\n", "    comparison_stats = df.groupby('isFraud')['amount'].agg(['count', 'mean', 'median', 'std']).round(2)\n", "    comparison_stats.index = ['Normal', 'Fraud']\n", "    display(comparison_stats)\n", "    \n", "else:\n", "    print(\"Cannot proceed with fraud analysis - dataset not loaded.\")"]}, {"cell_type": "markdown", "id": "ba32a752", "metadata": {}, "source": ["## Balance Analysis\n", "\n", "Let's analyze the balance patterns:\n", "1. Balance changes in transactions\n", "2. Relationship between old and new balances\n", "3. Balance patterns in fraudulent transactions\n", "4. Zero-balance transactions"]}, {"cell_type": "code", "execution_count": null, "id": "c0e81b13", "metadata": {}, "outputs": [], "source": ["# Only proceed if dataset was loaded successfully\n", "if df is not None:\n", "    print(\"=== BALANCE ANALYSIS ===\")\n", "    \n", "    # Calculate balance changes\n", "    df['balance_change_orig'] = df['newbalanceOrig'] - df['oldbalanceOrg']\n", "    df['balance_change_dest'] = df['newbalanceDest'] - df['oldbalanceDest']\n", "    \n", "    # Check for balance inconsistencies (potential data quality issues)\n", "    print(\"Balance consistency checks:\")\n", "    \n", "    # For most transaction types, the amount should equal the balance change\n", "    # (This might not hold for all transaction types in the simulation)\n", "    inconsistent_orig = df[abs(df['balance_change_orig'] + df['amount']) > 0.01]\n", "    inconsistent_dest = df[abs(df['balance_change_dest'] - df['amount']) > 0.01]\n", "    \n", "    print(f\"- Transactions with origin balance inconsistencies: {len(inconsistent_orig):,}\")\n", "    print(f\"- Transactions with destination balance inconsistencies: {len(inconsistent_dest):,}\")\n", "    \n", "    # Create comprehensive balance analysis\n", "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "    \n", "    # 1. Balance changes by transaction type (Origin)\n", "    df.boxplot(column='balance_change_orig', by='type', ax=axes[0,0])\n", "    axes[0,0].set_title('Origin Balance Changes by Transaction Type')\n", "    axes[0,0].set_xlabel('Transaction Type')\n", "    axes[0,0].set_ylabel('Balance Change')\n", "    axes[0,0].tick_params(axis='x', rotation=45)\n", "    \n", "    # 2. Balance changes by transaction type (Destination)\n", "    df.boxplot(column='balance_change_dest', by='type', ax=axes[0,1])\n", "    axes[0,1].set_title('Destination Balance Changes by Transaction Type')\n", "    axes[0,1].set_xlabel('Transaction Type')\n", "    axes[0,1].set_ylabel('Balance Change')\n", "    axes[0,1].tick_params(axis='x', rotation=45)\n", "    \n", "    # 3. Zero-balance analysis\n", "    zero_balance_data = {\n", "        'Origin Old': (df['oldbalanceOrg'] == 0).mean() * 100,\n", "        'Origin New': (df['newbalanceOrig'] == 0).mean() * 100,\n", "        'Dest Old': (df['oldbalanceDest'] == 0).mean() * 100,\n", "        'Dest New': (df['newbalanceDest'] == 0).mean() * 100\n", "    }\n", "    \n", "    axes[1,0].bar(zero_balance_data.keys(), zero_balance_data.values(), color='orange', alpha=0.7)\n", "    axes[1,0].set_title('Percentage of Zero-Balance Occurrences')\n", "    axes[1,0].set_ylabel('Percentage (%)')\n", "    axes[1,0].tick_params(axis='x', rotation=45)\n", "    axes[1,0].grid(axis='y', alpha=0.3)\n", "    \n", "    # 4. Balance patterns in fraud vs normal transactions\n", "    fraud_balance_orig = df[df['isFraud'] == 1]['oldbalanceOrg']\n", "    normal_balance_orig = df[df['isFraud'] == 0]['oldbalanceOrg']\n", "    \n", "    axes[1,1].hist([normal_balance_orig, fraud_balance_orig], bins=50, alpha=0.7,\n", "                   label=['Normal', 'Fraud'], color=['blue', 'red'])\n", "    axes[1,1].set_xlabel('Origin Balance Before Transaction')\n", "    axes[1,1].set_ylabel('Frequency')\n", "    axes[1,1].set_title('Origin Balance Distribution: Normal vs Fraud')\n", "    axes[1,1].legend()\n", "    axes[1,1].set_yscale('log')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Balance statistics by fraud status\n", "    print(\"\\n=== BALANCE STATISTICS BY FRAUD STATUS ===\")\n", "    balance_fraud_stats = df.groupby('isFraud')[['oldbalanceOrg', 'newbalanceOrig', 'oldbalanceDest', 'newbalanceDest']].mean().round(2)\n", "    balance_fraud_stats.index = ['Normal', 'Fraud']\n", "    display(balance_fraud_stats)\n", "    \n", "else:\n", "    print(\"Cannot proceed with balance analysis - dataset not loaded.\")"]}, {"cell_type": "markdown", "id": "f86e8095", "metadata": {}, "source": ["## Summary of Week 2 Findings\n", "\n", "### ✅ Completed Analysis:\n", "1. **Transaction Pattern Analysis**: Comprehensive analysis of transaction amounts, types, and temporal patterns\n", "2. **Fraud Detection Insights**: Deep dive into fraud patterns across transaction types and time\n", "3. **Balance Behavior Analysis**: Investigation of balance changes and zero-balance patterns\n", "4. **Statistical Comparisons**: Detailed statistical analysis of normal vs fraudulent transactions\n", "5. **Comprehensive Visualizations**: Created multiple chart types for different analytical perspectives\n", "\n", "### 📊 Key Insights Discovered:\n", "- **Transaction Volume**: Significant variation in transaction volumes across different types\n", "- **Fraud Concentration**: Fraud is concentrated in specific transaction types (TRANSFER and CASH_OUT)\n", "- **Amount Patterns**: Fraudulent transactions often involve larger amounts than normal transactions\n", "- **Temporal Patterns**: Fraud rates vary over time, suggesting potential cyclical patterns\n", "- **Balance Anomalies**: Zero-balance transactions are common and may indicate system behavior patterns\n", "- **Data Quality**: Identified potential balance inconsistencies that may need attention\n", "\n", "### 📈 Statistical Highlights:\n", "- Overall fraud rate is very low (< 1% of all transactions)\n", "- TRANSFER and CASH_OUT transactions have the highest fraud rates\n", "- Fraudulent transactions tend to have higher amounts on average\n", "- Zero-balance occurrences are frequent, especially for destination accounts\n", "\n", "### 📁 Deliverables Created:\n", "- Comprehensive EDA notebook with detailed analysis\n", "- Multiple visualization types (histograms, box plots, time series, pie charts)\n", "- Statistical summaries and comparisons\n", "- Data quality assessments and insights\n", "\n", "### 🎯 Next Steps (Week 3):\n", "- **Streamlit Dashboard Development**: Create interactive web application\n", "- **Interactive Visualizations**: Convert static charts to interactive Plotly charts\n", "- **Filtering System**: Implement sidebar filters for transaction type, fraud status, time periods\n", "- **Key Metrics Dashboard**: Display summary statistics and KPIs\n", "- **User Experience**: Add descriptions, tooltips, and user guidance\n", "\n", "### 🚀 Dashboard Features to Implement:\n", "1. **Overview Page**: Key metrics and summary statistics\n", "2. **Transaction Analysis**: Interactive charts for transaction patterns\n", "3. **Fraud Analysis**: Dedicated section for fraud insights\n", "4. **Time Series Analysis**: Temporal pattern exploration\n", "5. **Balance Analysis**: Balance behavior insights\n", "\n", "**Ready for Week 3 Dashboard Development!** 🎉"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}