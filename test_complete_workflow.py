#!/usr/bin/env python3
"""
Complete Workflow Test for Mobile Money Dashboard Project
Tests all components from data loading to dashboard functionality
"""

import os
import sys
import pandas as pd
import importlib.util
from datetime import datetime

def test_file_exists(filepath, description):
    """Test if a file exists and return status"""
    if os.path.exists(filepath):
        print(f"✅ {description}: Found")
        return True
    else:
        print(f"❌ {description}: Missing ({filepath})")
        return False

def test_data_loading():
    """Test data loading functionality"""
    print("\n=== TESTING DATA LOADING ===")
    
    # Test original dataset
    original_data = test_file_exists("PS_20174392719_1491204439457_log.csv", "Original PaySim Dataset")
    
    if original_data:
        try:
            df = pd.read_csv("PS_20174392719_1491204439457_log.csv")
            print(f"✅ Original dataset loaded: {len(df):,} rows, {len(df.columns)} columns")
            
            # Check required columns
            required_cols = ['step', 'type', 'amount', 'nameOrig', 'nameDest', 'isFraud', 
                           'oldbalanceOrg', 'newbalanceOrig', 'oldbalanceDest', 'newbalanceDest']
            missing_cols = [col for col in required_cols if col not in df.columns]
            
            if not missing_cols:
                print("✅ All required columns present")
            else:
                print(f"❌ Missing columns: {missing_cols}")
                return False
                
        except Exception as e:
            print(f"❌ Error loading original dataset: {e}")
            return False
    
    # Test cleaned dataset
    cleaned_data = test_file_exists("data/cleaned_transactions.csv", "Cleaned Dataset")
    
    if cleaned_data:
        try:
            df_clean = pd.read_csv("data/cleaned_transactions.csv")
            print(f"✅ Cleaned dataset loaded: {len(df_clean):,} rows, {len(df_clean.columns)} columns")
        except Exception as e:
            print(f"❌ Error loading cleaned dataset: {e}")
            return False
    
    return original_data and cleaned_data

def test_notebooks():
    """Test notebook files exist"""
    print("\n=== TESTING NOTEBOOKS ===")
    
    notebooks = {
        "Week 1 - Data Cleaning": "notebooks/Week1_Data_Loading_and_Cleaning.ipynb",
        "Week 2 - EDA": "notebooks/Week2_Exploratory_Data_Analysis.ipynb", 
        "Week 3 - Dashboard Dev": "notebooks/Week3_Dashboard_Development.ipynb",
        "Week 4 - Final Polish": "notebooks/Week4_Final_Polish.ipynb"
    }
    
    all_present = True
    for name, path in notebooks.items():
        if not test_file_exists(path, name):
            all_present = False
    
    return all_present

def test_dashboard():
    """Test dashboard components"""
    print("\n=== TESTING DASHBOARD ===")
    
    # Check dashboard file
    dashboard_exists = test_file_exists("src/app.py", "Streamlit Dashboard")
    
    if dashboard_exists:
        try:
            # Try to import the dashboard (basic syntax check)
            spec = importlib.util.spec_from_file_location("app", "src/app.py")
            print("✅ Dashboard file syntax appears valid")
        except Exception as e:
            print(f"❌ Dashboard syntax error: {e}")
            return False
    
    # Check requirements
    requirements_exists = test_file_exists("requirements.txt", "Requirements File")
    
    if requirements_exists:
        try:
            with open("requirements.txt", "r") as f:
                requirements = f.read()
                required_packages = ["pandas", "streamlit", "plotly", "matplotlib", "seaborn"]
                missing_packages = [pkg for pkg in required_packages if pkg not in requirements.lower()]
                
                if not missing_packages:
                    print("✅ All required packages listed in requirements.txt")
                else:
                    print(f"⚠️ Potentially missing packages: {missing_packages}")
        except Exception as e:
            print(f"❌ Error reading requirements.txt: {e}")
    
    return dashboard_exists and requirements_exists

def test_documentation():
    """Test documentation files"""
    print("\n=== TESTING DOCUMENTATION ===")
    
    docs = {
        "README": "README.md",
        "Setup Script (Windows)": "setup.bat",
        "Run Script (Windows)": "run_dashboard.bat"
    }
    
    all_present = True
    for name, path in docs.items():
        if not test_file_exists(path, name):
            all_present = False
    
    return all_present

def generate_summary_report():
    """Generate final project summary"""
    print("\n" + "="*60)
    print("📊 MOBILE MONEY DASHBOARD PROJECT SUMMARY")
    print("="*60)
    
    # Project stats
    if os.path.exists("data/cleaned_transactions.csv"):
        df = pd.read_csv("data/cleaned_transactions.csv")
        print(f"📈 Dataset: {len(df):,} transactions analyzed")
        print(f"💰 Total Volume: ${df['amount'].sum():,.2f}")
        print(f"🚨 Fraud Rate: {df['isFraud'].mean()*100:.3f}%")
        print(f"📊 Transaction Types: {df['type'].nunique()}")
    
    # File count
    total_files = 0
    for root, dirs, files in os.walk("."):
        total_files += len(files)
    
    print(f"📁 Total Project Files: {total_files}")
    print(f"⏰ Test Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("\n🎯 PROJECT DELIVERABLES:")
    print("✅ Week 1: Data Loading & Cleaning")
    print("✅ Week 2: Exploratory Data Analysis") 
    print("✅ Week 3: Interactive Dashboard")
    print("✅ Week 4: Final Polish & Documentation")
    
    print("\n🚀 TO RUN THE DASHBOARD:")
    print("1. pip install -r requirements.txt")
    print("2. streamlit run src/app.py")
    print("3. Open http://localhost:8501")
    
    print("\n🎉 PROJECT COMPLETE! Ready for presentation!")

def main():
    """Run complete workflow test"""
    print("🧪 MOBILE MONEY DASHBOARD - COMPLETE WORKFLOW TEST")
    print("="*60)
    
    # Run all tests
    data_ok = test_data_loading()
    notebooks_ok = test_notebooks()
    dashboard_ok = test_dashboard()
    docs_ok = test_documentation()
    
    # Overall status
    print("\n" + "="*60)
    print("📋 OVERALL TEST RESULTS")
    print("="*60)
    
    tests = {
        "Data Loading": data_ok,
        "Notebooks": notebooks_ok, 
        "Dashboard": dashboard_ok,
        "Documentation": docs_ok
    }
    
    passed = sum(tests.values())
    total = len(tests)
    
    for test_name, result in tests.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOVERALL: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Project is ready for use!")
        generate_summary_report()
    else:
        print(f"\n⚠️ {total-passed} test(s) failed. Please address issues before proceeding.")
        print("\n📝 Common fixes:")
        print("- Run Week 1 notebook to generate cleaned data")
        print("- Ensure all notebook files are present")
        print("- Check that src/app.py exists and is valid")
        print("- Verify requirements.txt contains all dependencies")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
